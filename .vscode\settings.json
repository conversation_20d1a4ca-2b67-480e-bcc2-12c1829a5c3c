{
	"files.encoding":"utf8",
	"files.associations": {
		"*.ui":"xml"
	},

	// ==================== 括号着色配置 ====================
	// 强制启用内置括号着色功能
	"editor.bracketPairColorization.enabled": true,

	// 强制显示括号对的引导线
	"editor.guides.bracketPairs": true,

	// 显示水平括号引导线
	"editor.guides.bracketPairsHorizontal": true,

	// 高亮显示活动的缩进引导线
	"editor.guides.highlightActiveIndentation": true,

	// 高亮显示活动的括号对
	"editor.matchBrackets": "always",

	// 括号对颜色独立循环
	"editor.bracketPairColorization.independentColorPoolPerBracketType": true,

	// 强制禁用可能冲突的扩展
	"bracket-pair-colorizer-2.depreciation-notice": false,
	"bracket-pair-colorizer.depreciation-notice": false,

	// ==================== Lua 文件特别配置 ====================
	"[lua]": {
		"editor.bracketPairColorization.enabled": true,
		"editor.guides.bracketPairs": "active",
		"editor.wordWrap": "on",
		"editor.tabSize": 4,
		"editor.insertSpaces": false,
		"editor.detectIndentation": false
	},

	// ==================== 通用编辑器优化 ====================
	// 显示空白字符（可选，帮助查看缩进）
	"editor.renderWhitespace": "boundary",

	// 高亮当前行
	"editor.renderLineHighlight": "all",

	// 显示缩进引导线
	"editor.guides.indentation": true
}