
local 作者UUID = "5d34a272-d237-4c96-b715-19846928e16d"--替换成自己的
local 卡密UUID  ="66e33afb-454e-489b-8117-0de2951f67aa"--替换成自己的
local 卡密密钥 = "facc1432-1467-4a25-bf00-1a9b97616107"--替换成自己的
local 云控UUID = "1e81a8f516984a67ac8f314a39e4322c" --创建项目会生成

local 服务器地址 = "api.privateapi.xyz"

arr = jsonLib.decode(getUIConfig("醉月脚本.config"))

local 卡密  = arr["page1"]["输入卡密"]

卡密 = 卡密:gsub("[%c%p]", "")
function 初始化叮当猫()
	
	local 初始化结果 = DDMControl.初始化(服务器地址, "9000", 云控UUID, 作者UUID, 云UIUUID)
	
end


--[===[function 叮当猫初始化km()

local 初始化结果km = DDMControl.初始化(服务器地址, "9000", "", 作者UUID, "")

end
]===]


function 心跳回调(msg)
	print(msg)
	if msg.code == 1 then
		print("心跳成功")
		网络验证失败次数 = 0
	elseif msg.code == -5 then
		print("请重新登录,一般是卡密被禁用,删除,设备被解绑!")
		toast("请重新登录,一般是卡密被禁用,删除,设备被解绑!")
		sleep(2000)
		exitScript()
	elseif msg.code == -8 then
		print("卡密到期")
		toast("卡密到期")
		sleep(2000)
		exitScript()
	elseif msg.code == -9999 then
		print("心跳失败,网络错误!")
		
	elseif msg.code == -11 then
		print("未知错误!", msg.msg)
		toast("错误原因:" .. msg.msg, 0, 0, 12)
	elseif msg.code == -6666 then
		print("有人尝试破解卡密系统!", msg.cdkey)
		exitScript()
	else
		print("未知错误!", msg.msg)
		toast("错误原因:" .. msg.msg, 0, 0, 12)
		exitScript()
	end
end

function 卡密登录(登录的卡密)
	
	local 登录结果 = DDMControl.卡密_卡密登录(卡密UUID, 卡密密钥, 登录的卡密, 心跳回调, true, true, 60)
	print(登录结果)
	
	if 登录结果.code == 0 then
		print("卡密被禁用")
		toast("卡密被禁用", 0, 0, 12)
		sleep(2000)
		exitScript()
	elseif 登录结果.code == -1 then
		print("网络错误,请检查网络!")
		
	elseif 登录结果.code == 1 then
		print("卡密登录成功!")
		toast("登录成功,到期时间:" .. 登录结果.endTime, 0, 0, 12)
		sleep(2000)
		
		
		
		return -- 登录成功，退出重试循环
	elseif 登录结果.code == -9 then
		print("卡密授权窗口达到上限,登录失败!")
		toast("卡密授权窗口达到上限,登录失败!", 0, 0, 12)
		sleep(2000)
		exitScript()
	elseif 登录结果.code == -7 then
		print("卡密过期!")
		toast("卡密过期!", 0, 0, 12)
		sleep(2000)
		exitScript()
	else
		print("未知错误!", 登录结果.msg)
		toast(登录结果.msg, 0, 0, 12)
		sleep(2000)
		exitScript()
	end
	
	
	--[===[toast("转备用服务器", 0, 0, 12)
	sleep(2000)]===]
	
end

function 功能代码()
	while 1 do
		sleep(500)
        各类弹窗()
		
		杀戮()
		
		世界boss封装()
		--神秘()
		挂星图()
		
		
	end
end
codebaidu=0
codeDDM=0


function 脚本开始函数()
	
	for i=1,2 do
		sleep(1000)
		print(i)
		retbaidu, codebaidu = httpGet("http://www.baidu.com",10) -- 发送 GET 请求
		retDDM, codeDDM = httpGet("http://api.privateapi.xyz:9000",10)
		if codebaidu==200 and  codeDDM==200 then
			break
		end
	end
	if codebaidu~=200 then
		exitScript()
		
	end
	
	if codebaidu==200 and  codeDDM==200 then
		DDMControl = require("DDMControlV2")
		初始化叮当猫()
		DDMControl.云控_连接云控系统()
		--叮当猫初始化km()
		for i=1,3 do
			local 更新结果 = DDMControl.热更新_检测更新并直接更新(60,true)
			if 更新结果.code ~= -9999 then
				break
			end
			sleep(1000)
		end
		
		卡密登录(卡密) ---下面写你自己的代码
	
		功能代码()
		
	elseif codebaidu==200 and  codeDDM~=200 then
		
		功能代码()
		
	end
end








